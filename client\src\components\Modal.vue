<template>
  <v-dialog
    v-model="xshow"
    :persistent="showActions"
    :fullscreen="Boolean(isMobile)"
  >
    <v-card :class="id">
      <v-card-title class="headline">{{ title }}</v-card-title>
      <v-icon v-if="isMobile" class="close-button" @click="Cancel">
        mdi-close
      </v-icon>
      <div
        :class="{ 'v-dialog--error': error, 'v-dialog--warning': warning }"
        v-show="error || warning"
        v-html="error || warning"
      ></div>
      <div class="v-dialog--error-action" v-show="errorAction">
        <div v-html="errorAction"></div>
        <v-btn
          elevation="0"
          small
          text
          @click="$emit('error-action-no', errorAction)"
          >TIDAK</v-btn
        >
        <v-btn
          elevation="0"
          small
          color="primary"
          @click="$emit('error-action-yes', errorAction)"
          >YA!</v-btn
        >
      </div>
      <div
        class="v-dialog--content"
        :class="{ '--loading': loading, '--mobile': isMobile }"
      >
        <slot></slot>
      </div>

      <v-card-actions v-if="showActions">
        <slot name="left-action"> </slot>
        <v-spacer></v-spacer>
        <v-btn v-if="cancelText" color="disabled" text @click="Cancel">
          {{ cancelText }}
        </v-btn>
        <v-btn
          color="green darken-2"
          text
          :loading="saving || loading"
          @click="Submit"
          v-if="submitText && !disabled && AllowWrite"
        >
          {{ submitText }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
  }),
  props: {
    show: Boolean,
    error: String,
    errorAction: String,
    warning: String,
    title: String,
    disabled: Boolean,
    loading: {
      type: Boolean,
      default: false,
    },
    saving: {
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
      default: 'Batal',
    },
    submitText: {
      type: String,
      default: 'Simpan',
    },
    showActions: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    id() {
      return 'modal-' + this.title.toLowerCase().replace(/\s/g, '-')
    },
  },
  watch: {
    xshow(val) {
      this.$emit('update:show', val)
    },
    show(val) {
      this.xshow = val
      if (val) {
        document.querySelector('.v-application--wrap').style.filter =
          'blur(4px)'
      } else {
        document.querySelector('.v-application--wrap').style.filter = 'none'
        this.$emit('update:show', val)
      }
    },
  },
  methods: {
    Submit() {
      this.$emit('onSubmit')
    },
    Cancel() {
      this.$emit('update:show', false)
      this.$emit('onCancel')
    },
  },
}
</script>
<style lang="scss">
.v-dialog {
  width: auto !important;
  /*overflow: hidden !important;*/
  &--error {
    background: orangered;
    color: white;
    padding: 10px;
    text-align: center;
    font-size: 14px;
  }
  &--error-action {
    background: #f3f3f3;
    padding: 10px;
    text-align: center;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .--warning {
    background: yellow;
    color: black;
    padding: 10px;
    text-align: center;
    font-size: 14px;
  }
  &--content {
    padding: 0 20px;
    max-height: calc(90vh - 110px);
    overflow: auto;

    &.--loading {
      filter: blur(4px);
    }
    &.--mobile {
      max-height: calc(100vh - 110px);
    }
  }
  &.v-dialog--fullscreen {
    .v-card__actions {
      width: 100vw;
    }
  }
  .page-header {
    display: none;
  }
  .close-button {
    position: fixed;
    top: 20px;
    right: 20px;
  }
}
</style>
