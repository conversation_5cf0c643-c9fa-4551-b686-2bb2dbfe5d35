<template>
  <v-container style="max-width: 100vw; padding: 0">
    <div class="login-box">
      <div
        style="
          height: 100px;
          background: url('/imgs/appex/banner.png');
          background-size: cover;
          background-position: center;
        "
      ></div>
      <div style="padding: 30px">
        <XInput
          label="User / Email / Whatsapp"
          placeholder="08XXXXXXXXX"
          :value.sync="forms.username"
          class="inline user-input"
          :left-icon="userIcon"
          width="100%"
        />
        <!-- <XInput
          label="Password"
          :value.sync="forms.password"
          type="password"
          left-icon="lock"
          width="100%"
        /> -->
        <div v-if="loginMode == 'otp' || !fido2Supported">
          <InputOtp
            label="OTP"
            :value.sync="forms.OTP"
            type="text"
            left-icon="lock"
            width="100%"
            @complete="otpComplete"
          />
          <br />
          <p style="color: gray; font-size: 14px">
            Anda dapat mendaftarkan nomor anda melalui korwil <PERSON>sperakim
          </p>
          <br />
          <v-btn
            color="primary"
            text
            @click="sendOtp"
            style="width: 245px; margin-left: -5px"
          >
            Kirim OTP
          </v-btn>
          <v-btn
            color="primary"
            :loading="isLoading"
            @click="submit"
            style="width: 245px; margin-bottom: 8px; margin-left: -3px"
            :disabled="!forms.OTP"
          >
            Login
          </v-btn>
          <v-btn
            small
            text
            color="success"
            @click="loginMode = 'biometric'"
            style="
              width: 245px;
              margin-bottom: 8px;
              margin-left: -3px;
              margin-top: 0px;
            "
          >
            Login Dengan Biometrik
          </v-btn>
        </div>
        <div v-else>
          <v-btn
            v-if="fido2Supported"
            color="success"
            :loading="isFido2Loading"
            @click="authenticateWithFIDO2"
            style="width: 245px; margin-bottom: 8px; margin-left: -3px"
            :disabled="isLoading"
          >
            <v-icon left>mdi-fingerprint</v-icon>
            Login Biometrik
          </v-btn>
          <v-btn small text color="primary" @click="loginMode = 'otp'">
            Login Dengan OTP
          </v-btn>
        </div>
      </div>
      <v-btn
        text
        small
        color="primary"
        style="margin-top: -20px"
        @click="showNews = true"
        v-show="!showNews"
        v-if="false"
      >
        <v-icon left>mdi-information-outline</v-icon> Apa yang baru?
      </v-btn>
    </div>

    <div
      style="
        height: 80px;
        background: rgba(255, 255, 255, 0.5);
        display: flex;
        justify-content: center;
        position: absolute;
        width: 100vw;
        bottom: 0;
      "
    >
      <div
        style="
          display: flex;
          align-self: center;
          text-align: center;
          width: 50vw;
          min-width: 350px;
        "
      >
        <a
          href="http://datartlh.perumahan.pu.go.id/"
          target="_blank"
          style="flex: 1"
        >
          <img src="/imgs/ertlh.png" height="30px" />
        </a>

        <a
          href="https://caribdt.dinsos.jatengprov.go.id/public/dashboard"
          target="_blank"
          style="flex: 1"
        >
          <img
            src="https://sidesa.jatengprov.go.id/img/onscreen/home/<USER>"
            height="30px"
          />
        </a>

        <a
          href="https://caribdt.dinsos.jatengprov.go.id/public/dashboard"
          target="_blank"
          style="flex: 1"
        >
          <img src="/imgs/caribdt-min.png" height="30px" />
        </a>
      </div>
    </div>
    <NewsUpdate :show="showNews" />
  </v-container>
</template>

<script>
import NewsUpdate from './NewsUpdate.vue'
import { mapActions } from 'vuex'
import {
  startRegistration,
  startAuthentication,
  browserSupportsWebAuthn,
} from '@simplewebauthn/browser'

export default {
  components: {
    NewsUpdate,
  },
  data: () => ({
    forms: {
      username: '',
      password: '',
    },
    loginMode: 'biometric',
    showNews: false,
    isLoading: false,
    isFido2Loading: false,
    fido2Supported: false,
  }),
  computed: {
    userIcon() {
      return this.forms.username.match(/^0[0-9]+$/)
        ? 'phone'
        : this.forms.username.match(/@/)
        ? 'email'
        : 'face'
    },
  },
  async mounted() {
    const basics = localStorage.getItem('basics')
    localStorage.clear()
    if (basics) localStorage.setItem('basics', basics)
    sessionStorage.clear()
    this.setMenu(null)

    // Check FIDO2/WebAuthn support
    if (basics) {
      const basicData = JSON.parse(basics)
      if (!basicData.hasBiometric) this.loginMode = 'otp'
      if (basicData.username) this.forms.username = basicData.username
    } else {
      this.loginMode = 'otp'
    }
    this.fido2Supported = browserSupportsWebAuthn()
    if (!this.fido2Supported) {
      console.warn('WebAuthn is not supported in this browser')
    }
  },
  methods: {
    ...mapActions(['setMenu', 'setUser', 'setIgahpUser']),
    getListMenu(menu) {
      let mlist = {}
      menu.forEach((m) => {
        if (m.child && m.child.length) {
          if (m.MenuUrl) {
            mlist[m.MenuUrl] = this.getListMenu(m.child)
          } else mlist = Object.assign(mlist, this.getListMenu(m.child))
        } else if (m.MenuUrl) mlist[m.MenuUrl] = m.RWX
      })
      return mlist
    },
    otpComplete() {
      if (this.forms.OTP?.length == 6) this.submit()
    },
    sendOtp() {
      if (!this.forms.username) {
        this.$api.notify('Masukkan Email / Nomor Whatsapp', 'error')
        return
      }

      this.$api.post(
        '/api/send-otp',
        {
          _Username: this.forms.username,
          _Purpose: 'login',
        },
        { notify: true }
      )
    },
    async loginIGAHP() {
      let d = await this.$api.post('/api/igahp/login', this.forms)
      if (d.success) {
        this.setIgahpUser({
          username: this.forms.username,
          accessToken: d.data.accessToken,
        })
        // sessionStorage.setItem(
        //   'igahpUser',
        //   JSON.stringify({
        //     username: this.forms.username,
        //     accessToken: d.data.accessToken,
        //   })
        // )
      }
      return d
    },
    async submit() {
      let ret = null
      if (!this.forms.username) {
        this.$api.notify('Masukkan Username', 'error')
        return
        // } else if (this.forms.username.match(/@[a-z0-9.]+$/)) {
        //   ret = await this.loginIGAHP()
        //   if (ret.success) {
        //     ret = await this.$api.login(this.forms)
        //   } else {
        //     this.$api.notify('Login Gagal', 'error')
        //     this.isLoading = false
        //     return
        //   }
      }

      if (!this.forms.OTP) {
        this.$api.notify('Masukkan OTP', 'error')
        return
      }

      this.isLoading = true
      ret = await this.$api.login(this.forms)

      if (ret && ret.length) {
        let basics = localStorage.getItem('basics')
        basics = basics ? JSON.parse(basics) : {}
        basics.username = this.forms.username
        localStorage.setItem('basics', JSON.stringify(basics))

        this.setUser(ret[0])
        let menu = await this.$api.call('Arch_SelMenu')
        if (menu) {
          this.setMenu(menu)
          localStorage.setItem(
            'menu-access',
            JSON.stringify(this.getListMenu(menu.data))
          )
          if (ret[0].HomeUrl) this.$router.push({ path: ret[0].HomeUrl })
          else this.$router.push({ name: 'Home' })
        }
      }
      this.isLoading = false
    },

    // FIDO2 Registration
    async registerFIDO2() {
      if (!this.fido2Supported) {
        this.$api.notify('Browser tidak mendukung WebAuthn', 'error')
        return
      }

      if (!this.forms.username) {
        this.$api.notify('Masukkan Username terlebih dahulu', 'error')
        return
      }

      this.isFido2Loading = true

      try {
        // Get registration options from server
        const optionsResponse = await this.$api.post(
          '/api/fido2/register/begin',
          {
            username: this.forms.username,
          }
        )

        if (!optionsResponse.success) {
          this.$api.notify(
            optionsResponse.message || 'Gagal memulai registrasi',
            'error'
          )
          return
        }

        // Start registration with the browser
        const credential = await startRegistration(optionsResponse.data)

        // Send credential to server for verification
        const verificationResponse = await this.$api.post(
          '/api/fido2/register/complete',
          {
            username: this.forms.username,
            credential,
          }
        )

        if (verificationResponse.success) {
          this.$api.notify('Biometrik berhasil didaftarkan!', 'success')
        } else {
          this.$api.notify(
            verificationResponse.message || 'Gagal mendaftarkan biometrik',
            'error'
          )
        }
      } catch (error) {
        console.error('FIDO2 registration error:', error)
        if (error.name === 'NotAllowedError') {
          this.$api.notify(
            'Registrasi dibatalkan atau tidak diizinkan',
            'error'
          )
        } else if (error.name === 'NotSupportedError') {
          this.$api.notify(
            'Perangkat tidak mendukung autentikasi biometrik',
            'error'
          )
        } else {
          this.$api.notify(
            'Gagal mendaftarkan biometrik: ' + error.message,
            'error'
          )
        }
      } finally {
        this.isFido2Loading = false
      }
    },

    // FIDO2 Authentication
    async authenticateWithFIDO2() {
      if (!this.fido2Supported) {
        this.$api.notify('Browser tidak mendukung WebAuthn', 'error')
        return
      }

      if (!this.forms.username) {
        this.$api.notify('Masukkan Username terlebih dahulu', 'error')
        return
      }

      this.isFido2Loading = true

      try {
        // Get authentication options from server
        const optionsResponse = await this.$api.post(
          '/api/fido2/authenticate/begin',
          {
            username: this.forms.username,
          }
        )

        if (!optionsResponse.success) {
          this.$api.notify(
            optionsResponse.message || 'Gagal memulai autentikasi',
            'error'
          )
          return
        }

        // Start authentication with the browser
        const credential = await startAuthentication(optionsResponse.data)

        // Send credential to server for verification
        const verificationResponse = await this.$api.post(
          '/api/fido2/authenticate/complete',
          {
            username: this.forms.username,
            credential,
          }
        )

        if (verificationResponse.success && verificationResponse.data) {
          let basics = localStorage.getItem('basics')
          basics = basics ? JSON.parse(basics) : {}
          basics.username = this.forms.username
          basics.hasBiometric = true
          localStorage.setItem('basics', JSON.stringify(basics))

          this.setUser(verificationResponse.data)
          if (verificationResponse.token) {
            this.$api.setToken(verificationResponse.token)
          }
          let menu = await this.$api.call('Arch_SelMenu')
          if (menu) {
            this.setMenu(menu)
            localStorage.setItem(
              'menu-access',
              JSON.stringify(this.getListMenu(menu.data))
            )
            if (verificationResponse.data.HomeUrl)
              this.$router.push({ path: verificationResponse.data.HomeUrl })
            else this.$router.push({ name: 'Home' })
          }
          this.$api.notify('Login berhasil dengan biometrik!', 'success')
        } else {
          this.$api.notify(
            verificationResponse.message || 'Autentikasi gagal',
            'error'
          )
        }
      } catch (error) {
        console.error('FIDO2 authentication error:', error)
        if (error.name === 'NotAllowedError') {
          this.$api.notify(
            'Autentikasi dibatalkan atau tidak diizinkan',
            'error'
          )
        } else if (error.name === 'NotSupportedError') {
          this.$api.notify(
            'Perangkat tidak mendukung autentikasi biometrik',
            'error'
          )
        } else {
          this.$api.notify(
            'Gagal autentikasi biometrik: ' + error.message,
            'error'
          )
        }
      } finally {
        this.isFido2Loading = false
      }
    },
  },
}
</script>
<style lang="scss">
// .theme--light.v-application {
//   background: #f3f3f3;
// }
.login-box {
  width: 334px;
  background: white;
  box-sizing: content-box;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  margin: auto;
  margin-top: 10%;
  text-align: center;
  overflow: hidden;

  .user-input {
    margin-bottom: 16px;

    .form-label {
      margin-bottom: 8px;
    }

    input,
    input:hover,
    input:focus {
      border: 2px solid #e0e0e0;
      border-radius: 4px;
      padding: 8px;
    }
  }
}
</style>
