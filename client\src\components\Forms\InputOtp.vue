<template>
  <div class="form-coms ui-otp">
    <div
      class="form-label"
      :class="{ '--required': hasError }"
      v-if="$attrs.label"
    >
      <slot name="label">
        {{ $attrs.label }}
      </slot>
    </div>
    <div class="--otp-container">
      <input
        v-for="(digit, index) in digits"
        :key="index"
        :ref="`otpInput${index}`"
        type="text"
        class="--otp-input"
        :class="{ '--error': hasError }"
        :value="digit"
        :disabled="$attrs.disabled"
        :readonly="$attrs.readonly"
        maxlength="1"
        @input="handleInput(index, $event)"
        @keydown="handleKeydown(index, $event)"
        @paste="handlePaste($event)"
        @focus="handleFocus(index)"
        @blur="handleBlur"
      />
    </div>
    <div class="--remarks" v-if="remarks">
      {{ remarks }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'InputOtp',
  data() {
    return {
      digits: ['', '', '', '', '', ''],
      isFocused: false,
    }
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    length: {
      type: Number,
      default: 6,
    },
    remarks: String,
  },
  computed: {
    otpValue() {
      return this.digits.join('')
    },
    hasError() {
      return (
        this.$attrs.label &&
        this.$attrs.label.match(/\*$/) &&
        (this.otpValue === null ||
          this.otpValue === undefined ||
          this.otpValue === '' ||
          this.otpValue.length < this.length)
      )
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        if (newValue !== this.otpValue) {
          this.setOtpValue(newValue || '')
        }
      },
    },
    otpValue(newValue) {
      this.$emit('update:value', newValue)
      this.$emit('input', newValue)

      // Emit complete event when all digits are filled
      if (newValue.length === this.length) {
        this.$emit('complete', newValue)
      }
    },
  },
  methods: {
    setOtpValue(value) {
      const cleanValue = String(value).replace(/\D/g, '').slice(0, this.length)
      this.digits = Array(this.length)
        .fill('')
        .map((_, index) => cleanValue[index] || '')
    },
    handleInput(index, event) {
      const value = event.target.value.replace(/\D/g, '') // Only allow digits

      // Update the current digit
      this.$set(this.digits, index, value)

      // Move to next input if value is entered and not the last input
      if (value && index < this.length - 1) {
        this.focusInput(index + 1)
      } else if (index == this.length - 1) {
        this.$emit('complete')
      }
    },
    handleMultipleInput(startIndex, value) {
      const digits = value.split('').slice(0, this.length - startIndex)

      digits.forEach((digit, i) => {
        const targetIndex = startIndex + i
        if (targetIndex < this.length) {
          this.$set(this.digits, targetIndex, digit)
        }
      })

      // Focus the next empty input or the last input
      const nextEmptyIndex = this.digits.findIndex(
        (digit, i) => i > startIndex && digit === ''
      )
      const focusIndex =
        nextEmptyIndex !== -1 ? nextEmptyIndex : this.length - 1
      this.focusInput(focusIndex)
    },
    handleKeydown(index, event) {
      // Handle backspace
      if (event.key === 'Backspace') {
        if (this.digits[index] === '' && index > 0) {
          // Move to previous input if current is empty
          this.focusInput(index - 1)
        } else {
          // Clear current input
          this.$set(this.digits, index, '')
        }
      }
      // Handle arrow keys
      else if (event.key === 'ArrowLeft' && index > 0) {
        event.preventDefault()
        this.focusInput(index - 1)
      } else if (event.key === 'ArrowRight' && index < this.length - 1) {
        event.preventDefault()
        this.focusInput(index + 1)
      }
      // Prevent non-numeric input
      else if (
        !/\d/.test(event.key) &&
        ![
          'Backspace',
          'Delete',
          'Tab',
          'Enter',
          'ArrowLeft',
          'ArrowRight',
        ].includes(event.key)
      ) {
        event.preventDefault()
      }
    },
    handlePaste(event) {
      event.preventDefault()
      const pastedData = (event.clipboardData || window.clipboardData).getData(
        'text'
      )
      const cleanData = pastedData.replace(/\D/g, '').slice(0, this.length)

      if (cleanData) {
        this.setOtpValue(cleanData)
        // Focus the next empty input or the last input
        const nextEmptyIndex = this.digits.findIndex((digit) => digit === '')
        const focusIndex =
          nextEmptyIndex !== -1 ? nextEmptyIndex : this.length - 1
        this.$nextTick(() => {
          this.focusInput(focusIndex)
        })
      }
    },
    handleFocus(index) {
      this.isFocused = true
      this.$emit('focus', index)
    },
    handleBlur() {
      this.isFocused = false
      this.$emit('blur', this.otpValue)
    },
    focusInput(index) {
      if (index >= 0 && index < this.length) {
        this.$nextTick(() => {
          const input = this.$refs[`otpInput${index}`]
          if (input && input[0]) {
            input[0].focus()
          }
        })
      }
    },
    clear() {
      this.digits = Array(this.length).fill('')
      this.focusInput(0)
    },
    focus() {
      this.focusInput(0)
    },
  },
}
</script>

<style lang="scss">
.ui-otp {
  margin-bottom: 8px;
  font-size: 14px;

  .form-label {
    text-align: left;
    margin-bottom: 8px;

    &.--required {
      color: red;
    }
  }

  .--otp-container {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .--otp-input {
    width: 40px;
    height: 40px;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    background: rgba(200, 200, 200, 0.1);
    transition: all 0.2s ease;

    &:hover {
      border-color: #bdbdbd;
      background: rgba(200, 200, 200, 0.2);
    }

    &:focus {
      outline: none;
      border-color: #1976d2;
      background: rgba(25, 118, 210, 0.1);
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }

    &:disabled {
      background: #f5f5f5;
      color: #9e9e9e;
      cursor: not-allowed;
    }

    &:readonly {
      background: #fafafa;
      cursor: default;
    }

    &.--error {
      border-color: #f44336;
      background: rgba(244, 67, 54, 0.1);

      &:focus {
        border-color: #f44336;
        box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
      }
    }
  }

  .--remarks {
    font-size: small;
    color: gray;
    margin-top: 4px;
  }
}

.dense {
  .ui-otp {
    font-size: 14px;

    .--otp-input {
      width: 35px;
      height: 35px;
      font-size: 16px;
    }
  }
}

// Mobile responsive
@media (max-width: 600px) {
  .ui-otp {
    .--otp-container {
      gap: 6px;
    }

    .--otp-input {
      width: 35px;
      height: 35px;
      font-size: 16px;
    }
  }
}
</style>
