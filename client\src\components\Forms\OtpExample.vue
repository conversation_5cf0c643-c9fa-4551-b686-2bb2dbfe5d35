<template>
  <div class="otp-example">
    <h2>OTP Component Examples</h2>
    
    <!-- Basic Usage -->
    <div class="example-section">
      <h3>Basic OTP Input</h3>
      <InputOtp 
        :value.sync="otp1" 
        label="Enter OTP"
        @complete="handleComplete"
        @input="handleInput"
      />
      <p>Current Value: {{ otp1 }}</p>
    </div>

    <!-- Required Field -->
    <div class="example-section">
      <h3>Required OTP Field</h3>
      <InputOtp 
        :value.sync="otp2" 
        label="Enter OTP*"
        remarks="Please enter the 6-digit code sent to your phone"
        @complete="handleComplete"
      />
      <p>Current Value: {{ otp2 }}</p>
    </div>

    <!-- Disabled State -->
    <div class="example-section">
      <h3>Disabled OTP</h3>
      <InputOtp 
        :value.sync="otp3" 
        label="Disabled OTP"
        disabled
      />
    </div>

    <!-- Readonly State -->
    <div class="example-section">
      <h3>Readonly OTP</h3>
      <InputOtp 
        :value.sync="otp4" 
        label="Readonly OTP"
        readonly
      />
    </div>

    <!-- Custom Length (4 digits) -->
    <div class="example-section">
      <h3>4-Digit OTP</h3>
      <InputOtp 
        :value.sync="otp5" 
        label="4-Digit PIN"
        :length="4"
        @complete="handleComplete"
      />
      <p>Current Value: {{ otp5 }}</p>
    </div>

    <!-- Actions -->
    <div class="example-section">
      <h3>Actions</h3>
      <button @click="clearAll">Clear All</button>
      <button @click="setTestValues">Set Test Values</button>
      <button @click="focusFirst">Focus First OTP</button>
    </div>
  </div>
</template>

<script>
import { InputOtp } from './index.js'

export default {
  name: 'OtpExample',
  components: {
    InputOtp,
  },
  data() {
    return {
      otp1: '',
      otp2: '',
      otp3: '123456',
      otp4: '654321',
      otp5: '',
    }
  },
  methods: {
    handleComplete(value) {
      console.log('OTP Complete:', value)
      this.$toast.success(`OTP entered: ${value}`)
    },
    handleInput(value) {
      console.log('OTP Input:', value)
    },
    clearAll() {
      this.otp1 = ''
      this.otp2 = ''
      this.otp5 = ''
    },
    setTestValues() {
      this.otp1 = '123456'
      this.otp2 = '789012'
      this.otp5 = '9876'
    },
    focusFirst() {
      // You can access the component methods using refs
      // this.$refs.otpRef.focus()
    },
  },
}
</script>

<style scoped>
.otp-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.example-section h3 {
  margin-top: 0;
  color: #333;
}

button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #1565c0;
}

p {
  margin: 10px 0;
  font-family: monospace;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}
</style>
