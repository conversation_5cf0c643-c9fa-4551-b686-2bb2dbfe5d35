<template>
  <Modal
    title="IMPORT DATA DARI EXCEL"
    :show.sync="xshow"
    width="300px"
    @onSubmit="Save"
    submitText=""
    cancelText="TUTUP"
  >
    <div style="width: 600px">
      <div style="padding: 20px; text-align: center" v-if="!failedRows">
        <Uploader @change="fileUploaded" accept=".xlsx">
          <template v-slot="{ opener, isLoading }">
            <v-btn
              color="primary"
              @click="opener"
              :disabled="isLoading || loading"
              >Upload File</v-btn
            >
          </template>
        </Uploader>
      </div>
      <div v-if="failedRows">
        <div style="font-size: 14px">
          Data berikut tidak dapat dimasukkan kedalam simperum:
        </div>
        <Grid
          :datagrid="failedRows"
          :disabled="true"
          :columns="[
            { name: 'NIK', value: 'NIK' },
            { name: '<PERSON><PERSON>', value: '<PERSON><PERSON>' },
            { name: '<PERSON><PERSON><PERSON>', value: 'Alamat' },
            { name: '<PERSON><PERSON>nga<PERSON>', value: 'Reason' },
          ]"
        ></Grid>
      </div>
    </div>
    <template v-slot:left-action>
      <v-btn text color="info" @click="downloadTemplate">
        Download Template
      </v-btn>
    </template>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    failedRows: null,
    loading: false,
  }),
  props: {
    show: Boolean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
      if (!val) this.failedRows = null
    },
  },
  methods: {
    downloadTemplate() {
      this.$api.download('/report/get/templates/SimperumImport.xlsx')
    },
    async fileUploaded(file) {
      this.loading = true
      let res = await this.$api.post(
        '/api/import',
        {
          from: file.data,
          to: 'raw_importexcel',
          sp: 'PRM_SavImportExcel',
        },
        { notification: true }
      )
      if (res.data.length) this.failedRows = res.data
      else this.xshow = false
      this.loading = false
    },
    async Save() {
      let ret = await this.api.call('', {})
      if (ret.success) this.emit('update:show', false)
    },
  },
}
</script>
