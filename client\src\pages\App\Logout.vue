<template>
  <div></div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  created() {
    this.setMenu([])
    this.setUser(null)
    this.setIgahpUser({})

    const basics = localStorage.getItem('basics')
    localStorage.clear()
    if (basics) localStorage.setItem('basics', basics)
    sessionStorage.clear()
    this.$router.push('/public')
  },
  methods: {
    ...mapActions(['setMenu', 'setUser', 'setIgahpUser']),
  },
}
</script>
