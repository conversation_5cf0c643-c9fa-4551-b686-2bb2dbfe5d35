# FIDO2 WebAuthn Implementation for SIMPERUM

This document describes the FIDO2 WebAuthn implementation added to the SIMPERUM login system.

## Overview

FIDO2 WebAuthn allows users to authenticate using biometric authentication (fingerprint, face recognition) or hardware security keys instead of passwords or OTP codes.

## Features Implemented

### Client-side (Vue.js)
- **Biometric Registration**: Users can register their biometric credentials
- **Biometric Authentication**: Users can login using registered biometric credentials
- **Browser Support Detection**: Automatically detects if the browser supports WebAuthn
- **Error Handling**: Comprehensive error handling for various FIDO2 scenarios

### Server-side (Node.js/Express)
- **Registration Endpoints**: 
  - `POST /api/fido2/register/begin` - Start credential registration
  - `POST /api/fido2/register/complete` - Complete credential registration
- **Authentication Endpoints**:
  - `POST /api/fido2/authenticate/begin` - Start authentication
  - `POST /api/fido2/authenticate/complete` - Complete authentication
- **Security**: Proper challenge generation and verification

## Dependencies Added

### Client
- `@simplewebauthn/browser`: ^9.0.1

### Server
- `@simplewebauthn/server`: ^9.0.3

## Database Schema Required

The following database schema needs to be created manually:

```sql
CREATE TABLE FIDO2_Credentials (
  ID INT IDENTITY(1,1) PRIMARY KEY,
  Username NVARCHAR(100) NOT NULL,
  CredentialID VARBINARY(MAX) NOT NULL, -- Store as binary data
  PublicKey VARBINARY(MAX) NOT NULL,
  Counter BIGINT NOT NULL DEFAULT 0,
  DeviceType NVARCHAR(50),
  BackedUp BIT DEFAULT 0,
  CreatedAt DATETIME2 DEFAULT GETDATE(),
  LastUsedAt DATETIME2,
  IsActive BIT DEFAULT 1
);

CREATE INDEX IX_FIDO2_Credentials_Username ON FIDO2_Credentials(Username);
CREATE INDEX IX_FIDO2_Credentials_CredentialID ON FIDO2_Credentials(CredentialID);
```

## Data Format Handling

**Important**: FIDO2 credential IDs come in different formats:

- **During Registration**: `verification.registrationInfo.credentialID` is a `Uint8Array(32)`
- **During Authentication**: `credential.id` is a Base64URL string (e.g., `'D2AxijDBLWHetycDnPlfugC_8nW4cUQ6jMfCObVnxUU'`)

The implementation handles this by:
1. **Registration**: Storing the `Uint8Array` directly as `VARBINARY` in database
2. **Authentication**: Converting Base64URL string to Buffer before database lookup:
   ```javascript
   const credentialIdBuffer = Buffer.from(credential.id, 'base64url')
   ```

## Stored Procedures Required

The following stored procedures need to be created manually:

1. **Arch_SavFIDO2Credential** - Save new credential during registration
2. **Arch_SelFIDO2Credentials** - Get all credentials for a user
3. **Arch_SelFIDO2Credential** - Get specific credential by ID
4. **Arch_UpdFIDO2Counter** - Update counter after successful authentication

## Usage

### For Users

1. **Registration**:
   - Enter username in the login form
   - Click "Daftarkan Biometrik" button
   - Follow browser prompts to register biometric/security key

2. **Authentication**:
   - Enter username in the login form
   - Click "Login dengan Biometrik" button
   - Follow browser prompts to authenticate

### Browser Support

FIDO2 WebAuthn is supported in:
- Chrome 67+
- Firefox 60+
- Safari 14+
- Edge 18+

The implementation automatically detects browser support and only shows FIDO2 options when supported.

## Security Considerations

1. **HTTPS Required**: FIDO2 only works over HTTPS in production
2. **Origin Validation**: Server validates the origin of authentication requests
3. **Challenge-Response**: Uses cryptographic challenges to prevent replay attacks
4. **Counter Validation**: Tracks authentication counter to detect cloned credentials

## Configuration

The FIDO2 configuration is automatically set based on environment:

- **Development**: `localhost:8000`
- **Production**: `simperum.disperakim.jatengprov.go.id`

## Error Handling

The implementation handles common FIDO2 errors:

- `NotAllowedError`: User cancelled or operation not allowed
- `NotSupportedError`: Device doesn't support the operation
- `InvalidStateError`: Credential already exists
- Network errors and server-side validation errors

## Next Steps

1. Install dependencies: `npm install` in both client and server directories
2. Create the database schema and stored procedures
3. Test the implementation in a supported browser
4. Deploy to production with HTTPS enabled

## Files Modified

- `client/package.json` - Added WebAuthn browser library
- `client/src/pages/App/Login.vue` - Added FIDO2 UI and logic
- `server/package.json` - Added WebAuthn server library  
- `server/api/call.js` - Added FIDO2 endpoints and logic
