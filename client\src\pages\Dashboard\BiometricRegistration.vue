<template>
  <Modal
    title="PENDAFTARAN LOGIN BIOMETRIK"
    :show.sync="xshow"
    width="400px"
    @onSubmit="Save"
  >
    <div>
      <InputOtp
        label="OTP"
        :value.sync="forms.OTP"
        type="text"
        left-icon="lock"
        width="100%"
        @complete="otpComplete"
      />
      <br />
      <v-btn color="primary" text @click="sendOtp" style="width: 250px">
        <PERSON><PERSON>TP
      </v-btn>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
  }),
  props: {
    show: <PERSON>olean,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.emit('update:show', val)
    },
  },
  methods: {
    sendOtp() {
      this.$api.post(
        '/api/send-otp',
        {
          _Phone: this.forms.username,
          _Purpose: 'login',
        },
        { notify: true }
      )
    },
    async Save() {
      let ret = await this.api.call('', {})
      if (ret.success) this.emit('update:show', false)
    },
  },
}
</script>
