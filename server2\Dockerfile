# Use Node.js 22 slim image as base
FROM node:22-slim

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application source code
COPY . .

# # Create a non-root user to run the application
# RUN groupadd -r appuser && useradd -r -g appuser appuser

# # Change ownership of the app directory to the appuser
# RUN chown -R appuser:appuser /app

# # Switch to non-root user
# USER appuser

# Expose the port the app runs on
EXPOSE 3000

# Set environment to production
ENV NODE_ENV=production

# Health check
# HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
#   CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "server.js"]