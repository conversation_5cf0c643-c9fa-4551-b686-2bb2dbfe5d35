const axios = require('axios')
const fs = require('fs')

module.exports = {
  async verifyCaptcha(token) {
    var res = await axios.post(
      `https://www.google.com/recaptcha/api/siteverify?secret=6LfoHqoUAAAAANktvh4ppo-OH2d7VnVqlwg8dCXC&response=${token}`,
      {},
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
        },
      }
    )

    return res.data
  },
  // async getExifData(img_path) {
  //   return new Promise((resolve, reject) => {
  //     const ExifImage = require('exif').ExifImage
  //     new ExifImage({image: img_path}, function(error, exifData) {
  //       console.log('sini', exifData)
  //       if (error) {
  //         if (error.message != 'No Exif segment found in the given image.' &&
  //             error.message != 'The given image is not a JPEG and thus unsupported right now.')
  //           console.log('Error: ' + error.message)
  //         resolve(null)
  //       } else {
  //         if (exifData && exifData.gps && exifData.gps.GPSLatitude) {
  //           let lat = exifData.gps.GPSLatitude
  //           exifData.gps.lat = module.exports.convertGPS_DMS2DD(
  //             lat[0],
  //             lat[1],
  //             lat[2],
  //             exifData.gps.GPSLatitudeRef
  //           )
  //           let lon = exifData.gps.GPSLongitude
  //           exifData.gps.lon = module.exports.convertGPS_DMS2DD(
  //             lon[0],
  //             lon[1],
  //             lon[2],
  //             exifData.gps.GPSLongitudeRef
  //           )
  //           resolve({gps:exifData.gps}) // Do something with your data!
  //         }
  //         console.log('No Exif Data: ' + img_path)
  //         resolve(null)
  //       }
  //     })
  //   })
  // },
  async getExifData(img_path) {
    const exifr = require('exifr')
    let exif = await exifr.parse(img_path)
    if (exif?.latitude) {
      return {
        gps: {
          lat: exif.latitude,
          lon: exif.longitude,
        },
      }
    } else if (exif?.gps?.GPSLatitude) {
      return {
        gps: {
          lat: module.exports.convertGPS_DMS2DD(
            exif.gps.GPSLatitude[0],
            exif.gps.GPSLatitude[1],
            exif.gps.GPSLatitude[2],
            exif.gps.GPSLatitudeRef
          ),
          lon: module.exports.convertGPS_DMS2DD(
            exif.gps.GPSLongitude[0],
            exif.gps.GPSLongitude[1],
            exif.gps.GPSLongitude[2],
            exif.gps.GPSLongitudeRef
          ),
        },
      }
    }
    return null
  },
  convertGPS_DMS2DD(days, minutes, seconds, direction) {
    direction.toUpperCase()
    var dd = days + minutes / 60 + seconds / (60 * 60)
    //alert(dd);
    if (direction == 'S' || direction == 'W') {
      dd = dd * -1
    } // Don't do anything for N or E
    return dd
  },
  generateOTP() {
    // Generate a 6-digit OTP
    return Math.floor(100000 + Math.random() * 900000).toString();
  },
  async sendWhatsapp(phone, msg) {
    return axios.post(`http://simperum.disperakim.jatengprov.go.id:2984/send`, {
      to: phone.replace('+', ''),
      message: msg,
      key: 'er1ganteng',
    })
  }
}
