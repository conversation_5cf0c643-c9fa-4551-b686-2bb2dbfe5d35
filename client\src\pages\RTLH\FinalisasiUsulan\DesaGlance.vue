<template>
  <div>
    <div id="dvjml" class="" style="width: calc(100% - 20px)">
      <div
        style="
          background: rgba(255, 255, 255, 0.5);
          padding: 15px 13px;
          font-family: Raleway;
          font-weight: bold;
        "
      >
        <span id="areaname"
          >{{ details.Kabupaten }}, {{ details.Kecamatan }},
          {{ details.Ke<PERSON>rahan }}</span
        >
        <span
          style="
            padding: 10px 20px;
            background: white;
            border-radius: 20px;
            margin-left: 20px;
          "
        >
          <i class="fa fa-shield" style="color: orangered"></i>
          &nbsp;
          <span id="StatusPBDT">BELUM BEBAS PBDT</span>
        </span>
      </div>
      <div
        v-if="!hideDetail"
        style="
          background: rgba(255, 255, 255, 0.7);
          padding: 5px 15px;
          font-family: Raleway;
          text-transform: uppercase;
          font-size: small;
        "
      >
        <span id="JmlRumah" style="margin-right: 20px"
          >JML RUMAH: <b>{{ details.JmlRumah }}</b></span
        >
        <span id="JmlPenduduk" style="margin-right: 20px"
          >JML PENDUDUK: <b>{{ details.JmlPenduduk }}</b></span
        >
        <span id="LuasArea" style="margin-right: 20px"
          >LUAS DESA: <b>{{ details.LuasArea }}</b></span
        >
        <span id="JmlBacklog" style="margin-right: 20px"
          >JML BACKLOG: <b>{{ details.JmlBacklog }}</b></span
        >
        <span id="JmlNonSertifikat" style="margin-right: 20px"
          >BELUM BERSERTIFIKAT: <b>{{ details.JmlNonSertifikat }}</b></span
        >
      </div>
      <div
        v-if="dbparams.Sumber && !hideDetail"
        style="
          background: rgba(255, 255, 255, 0.7);
          margin-bottom: 10px;
          padding: 5px 15px;
          font-family: Raleway;
          text-transform: uppercase;
          font-size: small;
          display: flex;
        "
      >
        <span id="JmlUsulan" style="margin-right: 20px">
          KUOTA: <b>{{ details.Kuota }}</b>
        </span>
        <span id="JmlKuota" style="margin-right: 20px">
          DIGUNAKAN: <b>{{ details.KuotaDigunakan }}</b>
        </span>
        <span id="JmlValidasi" style="margin-right: 20px">
          SISA: <b>{{ details.KuotaSisa }}</b>
        </span>
        <v-spacer />
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data: () => ({
    details: {},
    showSetujuiPopup: false,
  }),
  props: {
    dbref: {
      type: String,
      default: 'PRM.SelFinalisasiInfo',
    },
    dbparams: [String, Object],
    jmlUsulan: [Number, String],
    addNew: Boolean,
    hideDetail: Boolean,
    isApproved: Number,
  },
  watch: {
    dbparams() {
      this.populate()
    },
  },
  methods: {
    async populate() {
      let { data } = await this.$api.call(this.dbref, this.dbparams)
      this.details = data[0] || {}
    },
    async SavePengesahan(appv) {
      if (
        this.dbparams.Tahun >= 2022 &&
        appv === 1 &&
        this.dbparams.Sumber == 2
      ) {
        this.showSetujuiPopup = true
      } else {
        this.doSavePengesahan(null, appv)
      }
    },
  },
}
</script>
<style lang="scss"></style>
