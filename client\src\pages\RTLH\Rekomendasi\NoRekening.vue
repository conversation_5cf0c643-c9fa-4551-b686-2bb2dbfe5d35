<template>
  <Modal title="Rekening Bank" :show.sync="x_show" @onSubmit="Submit">
    <XInput
      type="text"
      label="No Rekening"
      :value.sync="form.NoRekening"
      width="300px"
    />
    <XInput
      type="text"
      label="Nama Rekening"
      :value.sync="form.NamaRekening"
      width="300px"
    />
    <XInput
      type="text"
      label="Cabang Pembuka"
      :value.sync="form.NamaBank"
      width="300px"
    />
  </Modal>
</template>
<script>
export default {
  data: () => ({
    x_show: false,
    form: {},
  }),
  props: {
    pids: String,
    show: <PERSON>ole<PERSON>,
  },
  watch: {
    nobdt() {
      this.form.NamaBank = ''
      this.form.NamaRekening = ''
      this.form.NoRekening = ''
    },
    show(val) {
      this.x_show = val
      if (val) this.Populate()
    },
    x_show() {
      this.$emit('update:show', this.x_show)
    },
  },
  methods: {
    async Populate() {
      this.form.ProposalDetIds = this.pids
      let d = await this.$api.call('PRM.SelRekeningBansos', this.form)
      if (d.data.length) {
        this.form = { ...d.data[0] }
      }
    },
    async Submit() {
      this.form.ProposalDetIds = this.pids
      let ret = await this.$api.call('PRM.SavRekeningBansos', this.form)
      if (ret.success) {
        this.$emit('success')
        this.$emit('update:show', false)
      }
    },
  },
}
</script>
<style lang="scss"></style>
