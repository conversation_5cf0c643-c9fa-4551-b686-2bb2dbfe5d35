<template>
  <!-- <div
    @click="isClicked = true"
    style="letter-spacing: 1.1px"
    :style="{ cursor: isClicked ? '' : 'pointer' }"
  >
    {{
      isClicked ? nik : nik?.replace(/^(.{3})(.{11})/, '$1 * * * * * * * * * ')
    }}
  </div> -->
  <div>
    <v-btn
      v-if="!isClicked"
      x-small
      elevation="0"
      color="primary"
      @click="decrypt"
      style="min-width: 120px"
    >
      DATA PRIBADI
    </v-btn>
    <div v-else style="line-height: 20px">
      {{ content || '( TIDAK ADA )' }}
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    isClicked: false,
    content: '',
  }),
  props: {
    nik: [Object, String],
  },
  watch: {
    nik() {
      this.isClicked = false
    },
  },
  methods: {
    async decrypt() {
      if (this.nik.type == 'Buffer') {
        let ret = await this.$api.call('Arch.SelDecrypt', {
          Content: this.nik,
        })
        this.content = ret.data[0].Content
      } else {
        this.content = this.nik
      }
      this.isClicked = true
    },
  },
}
</script>
<style lang="scss"></style>
