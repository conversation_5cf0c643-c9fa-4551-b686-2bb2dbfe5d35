<template>
  <Page title="Review Usulan" :sidebar="true">
    <Sidebar :value.sync="area" :tabs="[2, 4, 13]" />
    <div v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      />
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :disabled="true"
        :filter="filterGrid"
        height="calc(100vh - 243px)"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain',
          },
          {
            name: '<PERSON><PERSON>',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width plain',
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: '<PERSON><PERSON><PERSON>',
            value: '<PERSON>ama<PERSON>',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
          {
            name: '',
            value: 'Doku<PERSON>',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Checklist',
            class: 'plain center',
          },
          {
            name: '',
            value: 'HasMessage',
            class: 'plain center',
          },
          {
            name: approvalHeader,
            value: 'Approval',
            class: 'plain center',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox
            :value.sync="row.CheckedValue"
            checkedIcon="check_box"
            disabledIcon="mdi-lock"
            :disabled="Boolean(row.IsLocked)"
          />
        </template>
        <template v-slot:row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template v-slot:row-KRT_Nama="{ row }">
          <v-btn text small color="primary" @click="OpenDetail(row.NoRef)">
            {{ row.KRT_Nama }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
          >
            mdi-account-check
          </v-icon>
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-Dokumen="{ row }">
          <v-icon
            v-if="row.IsComplete"
            color="success"
            @click="OpenProposal(row.NIK)"
          >
            mdi-file-check
          </v-icon>
          <v-icon v-if="!row.IsComplete" @click="OpenProposal(row.NIK)">
            mdi-file-alert-outline
          </v-icon>
        </template>
        <template v-slot:row-Checklist="{ row }">
          <v-icon v-if="row.IsComplete" @click="OpenChecklist(row.NIK)">
            mdi-clipboard-list
          </v-icon>
        </template>
        <template v-slot:row-HasMessage="{ row }">
          <v-icon
            :color="row.HasMessage == 2 ? 'green' : 'silver'"
            v-tooltip="
              row.HasMessage == 2 ? 'Ada pesan baru' : 'tidak ada pesan baru'
            "
            @click="OpenMessages(row.NoRef)"
          >
            {{
              row.HasMessage ? 'mdi-message-text' : 'mdi-message-minus-outline'
            }}
          </v-icon>
        </template>
        <template v-slot:row-Approval="{ row }">
          <XSelect
            v-if="area.tabId == 4"
            :items="itemsCSR"
            :value.sync="row.Jenis"
          ></XSelect>
          <v-btn
            v-else
            :disabled="!(row.IsComplete || row.IsApproved)"
            text
            small
            color="success"
            @click="ApproveProposal(row)"
          >
            <v-icon left v-if="row.IsApproved">check_box</v-icon>
            <v-icon left v-if="!row.IsApproved">
              mdi-checkbox-blank-outline
            </v-icon>
            {{ [4, 13].includes(area.tabId) ? 'DICAIRKAN' : 'SETUJUI' }}
          </v-btn>
        </template>
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <component
        :is="modalDetail"
        :nik="selectedNIK"
        :show.sync="showProposalModal"
      />
      <ChecklistProposal :nik="selectedNIK" :show.sync="showChecklistModal" />
      <Messages
        :tahun="area.Tahun"
        :noRef="selectedRef"
        :show.sync="showMessages"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import ChecklistProposal from './Checklist.vue'
import Messages from './Messages.vue'
import ProposalBAZNAS from '../InputUsulan/BAZNAS.vue'
import ProposalCSR from '../InputUsulan/CSR.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ProposalBAZNAS,
    ProposalCSR,
    ChecklistProposal,
    Messages,
  },
  data: () => ({
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showMessages: false,
    selectedRef: null,
    selectedNIK: null,
    modalDetail: 'ProposalDetail',
    itemsCSR: [
      { text: 'Bank Jateng', value: 'Bank Jateng' },
      { text: 'Djarum', value: 'Djarum' },
      { text: 'Budha Tsuzie', value: 'Budha Tsuzie' },
      { text: 'BPR BKK', value: 'BPR BKK' },
      { text: 'KIW', value: 'KIW' },
      { text: 'Lainnya', value: 'Lainnya' },
    ],
  }),
  computed: {
    approvalHeader() {
      return [4, 13].includes(this.area.tabId) ? 'Oleh:' : 'Persetujuan'
    },
    dbref() {
      if (this.area.tabId == 2) {
        return 'PRM.ProposalDet'
      } else {
        return 'PRM.ProposalBSPS'
      }
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue && (d.FinalStatus || 'OK') == 'OK'
      }).length
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    'area.tabId'(val) {
      if (val == 4) this.modalDetail = 'ProposalCSR'
      else if (val == 13) this.modalDetail = 'ProposalBAZNAS'
      else this.modalDetail = 'ProposalDetail'
    },
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenChecklist(nik) {
      this.selectedNIK = nik
      this.showChecklistModal = true
    },
    OpenMessages(noRef) {
      this.selectedRef = noRef
      this.showMessages = true
    },
    async ApproveProposal(row) {
      let ret = await this.$api.call('PRM.SavProposalApproval', {
        ProposalDetID: ',' + row.ProposalDetID + ',',
        IsApproved: row.IsApproved ? 0 : 1,
      })
      if (ret.success) row.IsApproved = row.IsApproved ? 0 : 1
    },
    filterGrid(row) {
      return Boolean(row.IsLocked) || Boolean(row.CheckedValue)
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
