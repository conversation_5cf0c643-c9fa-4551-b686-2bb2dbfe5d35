<template>
  <v-card class="ma-4 pa-4">
    <v-card-title>
      <v-icon left>mdi-update</v-icon>
      Update System Tester
    </v-card-title>
    
    <v-card-text>
      <p class="mb-4">
        This component helps test the application update system.
      </p>
      
      <v-row>
        <v-col cols="12" md="6">
          <v-card outlined>
            <v-card-subtitle>Service Worker Status</v-card-subtitle>
            <v-card-text>
              <v-chip
                :color="swStatus.color"
                text-color="white"
                small
                class="mb-2"
              >
                {{ swStatus.text }}
              </v-chip>
              <br>
              <small class="grey--text">
                Registration: {{ registration ? 'Active' : 'None' }}
              </small>
            </v-card-text>
          </v-card>
        </v-col>
        
        <v-col cols="12" md="6">
          <v-card outlined>
            <v-card-subtitle>Update Status</v-card-subtitle>
            <v-card-text>
              <v-chip
                :color="updateStatus.color"
                text-color="white"
                small
                class="mb-2"
              >
                {{ updateStatus.text }}
              </v-chip>
              <br>
              <small class="grey--text">
                Last checked: {{ lastChecked }}
              </small>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      
      <v-divider class="my-4"></v-divider>
      
      <v-row>
        <v-col cols="12">
          <h4 class="mb-3">Test Actions</h4>
          <v-btn
            color="primary"
            @click="checkForUpdates"
            :loading="checking"
            class="mr-2 mb-2"
          >
            <v-icon left>mdi-refresh</v-icon>
            Check for Updates
          </v-btn>
          
          <v-btn
            color="warning"
            @click="simulateUpdate"
            :disabled="!canSimulate"
            class="mr-2 mb-2"
          >
            <v-icon left>mdi-play</v-icon>
            Simulate Update
          </v-btn>
          
          <v-btn
            color="info"
            @click="showUpdateInfo"
            class="mr-2 mb-2"
          >
            <v-icon left>mdi-information</v-icon>
            Update Info
          </v-btn>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'UpdateTester',
  data() {
    return {
      registration: null,
      checking: false,
      lastChecked: 'Never',
      canSimulate: false,
    }
  },
  
  computed: {
    swStatus() {
      if (!('serviceWorker' in navigator)) {
        return { color: 'red', text: 'Not Supported' }
      }
      if (this.registration) {
        return { color: 'green', text: 'Active' }
      }
      return { color: 'orange', text: 'Loading...' }
    },
    
    updateStatus() {
      if (this.$parent.updateAvailable) {
        return { color: 'orange', text: 'Update Available' }
      }
      if (this.$parent.isUpdating) {
        return { color: 'blue', text: 'Updating...' }
      }
      return { color: 'green', text: 'Up to Date' }
    }
  },
  
  async mounted() {
    await this.initServiceWorker()
  },
  
  methods: {
    async initServiceWorker() {
      if ('serviceWorker' in navigator) {
        try {
          this.registration = await navigator.serviceWorker.ready
          this.canSimulate = true
        } catch (error) {
          console.error('Error getting service worker registration:', error)
        }
      }
    },
    
    async checkForUpdates() {
      this.checking = true
      this.lastChecked = new Date().toLocaleTimeString()
      
      try {
        if (this.registration) {
          await this.registration.update()
          this.$toast.open({
            message: 'Update check completed',
            type: 'info'
          })
        }
      } catch (error) {
        console.error('Error checking for updates:', error)
        this.$toast.open({
          message: 'Error checking for updates',
          type: 'error'
        })
      } finally {
        this.checking = false
      }
    },
    
    simulateUpdate() {
      // Simulate an update by triggering the parent's update system
      this.$parent.updateAvailable = true
      this.$parent.waitingWorker = { 
        postMessage: (msg) => {
          console.log('Simulated worker received message:', msg)
          // Simulate the controllerchange event after a delay
          setTimeout(() => {
            this.$parent.isUpdating = false
            this.$toast.open({
              message: 'Simulated update completed!',
              type: 'success'
            })
          }, 3000)
        }
      }
      
      this.$toast.open({
        message: 'Update simulation triggered',
        type: 'info'
      })
    },
    
    showUpdateInfo() {
      const info = {
        'Service Worker Support': 'serviceWorker' in navigator,
        'Registration Status': this.registration ? 'Active' : 'None',
        'Update Available': this.$parent.updateAvailable,
        'Currently Updating': this.$parent.isUpdating,
        'Update Progress': this.$parent.updateProgress + '%',
        'Update Status': this.$parent.updateStatus,
      }
      
      console.table(info)
      this.$toast.open({
        message: 'Update info logged to console',
        type: 'info'
      })
    }
  }
}
</script>
