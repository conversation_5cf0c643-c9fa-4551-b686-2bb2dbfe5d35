import Vue from 'vue'
import {
  XInput,
  InputOtp,
  XSelect,
  DatePicker,
  Search,
  TextArea,
  Checkbox,
  List,
  XLabel,
  Map,
  MenuButton,
} from '../components/Forms'
import Page from '../components/Page.vue'
import Grid from '../components/Grid/index.vue'
import Modal from '../components/Modal.vue'
import Uploader from '../components/Uploader/index.vue'
import Panel from '../components/Panel.vue'

export default {
  register() {
    Vue.component('XInput', XInput)
    Vue.component('InputOtp', InputOtp)
    Vue.component('XSelect', XSelect)
    //eslint.vuejs.org/rules/multi-word-component-names.html
    Vue.component('Search', Search)
    Vue.component('DatePicker', DatePicker)
    Vue.component('TextArea', TextArea)
    Vue.component('Checkbox', Checkbox)
    Vue.component('Page', Page)
    Vue.component('Grid', Grid)
    Vue.component('Modal', Modal)
    Vue.component('List', List)
    Vue.component('XLabel', XLabel)
    Vue.component('Uploader', Uploader)
    Vue.component('Map', Map)
    Vue.component('MenuButton', MenuButton)
    Vue.component('Panel', Panel)
  },
}
