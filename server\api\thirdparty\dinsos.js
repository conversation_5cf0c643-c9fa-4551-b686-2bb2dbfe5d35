const axios = require("axios");
var db = require("../../common/db");

const creds = `username=disperakim&token=eIJCVbmZfYdusKiqqVDN`
const url = `https://caribdt.dinsos.jatengprov.go.id/api`


module.exports = {
  async getNIKOld(nik) {
    let d = await axios
      .get(`${url}/nik_perakim9501/?${creds}&nik=${nik}`)
      .catch((err) => {
        console.log(`get_nikold_perakim:`+err.response?.statusText);
      });
    return d ? d.data : {};
  },
  async getByAreaOld(kodeDagri) {
    kodeDagri = kodeDagri+''
    let kdkab = kodeDagri.substr(2, 2)
    let kdkec = kodeDagri.substr(4, 2)
    let kddesa = kodeDagri.substr(-4)
    let d = await axios
      .get(`${url}/desa_perakim9502/?${creds}&kdkab=${kdkab}&kdkec=${kdkec}&kddesa=${kddesa}`)
      .catch((err) => {
        console.log(`get_byarea_perakim:`+err.response?.statusText);
      });
    return d.data
  },
  async getNIK(nik) {
    console.log(nik)
    let form = new FormData();
    form.append('username', 'perakim');
    form.append('password', 'apiperakim');
    form.append('nik', nik);

    let d = await axios
      .post(`https://dtjateng.dinsos.jatengprov.go.id/api/perakim/cek-data-nik`, form)
      .catch((err) => {
        console.log(`get_nik_perakim: `+err);
      });
    let rd = d ? d.data : {}
    rd.alamat = rd.alamat || ""
    return rd;
  },
  async getByArea(kodeDagri) {
    kodeDagri = kodeDagri+''
    let kdkab = kodeDagri.substr(2, 2)
    let kdkec = kodeDagri.substr(4, 2)
    let kddesa = kodeDagri.substr(-4)

    let form = new FormData();
    form.append('username', 'perakim');
    form.append('password', 'apiperakim');
    form.append('kdkab', kdkab);
    form.append('kdkec', kdkec);
    form.append('kddesa', kddesa);

    let d = await axios
      .post(`https://caribdt.dinsos.jatengprov.go.id/api/perakim/cek-data-desa`, form)
      .catch((err) => {
        console.log(`get_byarea_perakim:`+err.response?.statusText);
      });
    return d.data
  },
};
