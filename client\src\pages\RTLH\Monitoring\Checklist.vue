<template>
  <Modal
    title="DETAIL MONITORING"
    :show.sync="xshow"
    width="1000px"
    @onSubmit="Save"
  >
    <div class="form-inline" style="width: 780px">
      <div class="iblock" style="width: 300px">
        <div style="padding: 5px">
          <Uploader
            style="
              width: 80px;
              height: 80px;
              display: inline-block;
              margin-right: 8px;
            "
            :value.sync="forms.Profile"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
          <div class="iblock">
            <!-- <div style="font-size: small">{{ forms.NIK }}</div> -->
            <div>{{ forms.Nama }}</div>
            <div
              style="
                font-size: small;
                color: gray;
                width: 200px;
                height: 40px;
                overflow: hidden;
              "
            >
              {{ forms.Alamat }}
            </div>
          </div>
        </div>
        <div class="iblock dv-kerusakan" style="width: 100%">
          <div style="padding: 0px; font-weight: bold">
            DATA SEBELUM / SESUDAH
          </div>
          <div style="padding: 5px" class="inline-form">
            <div style="display: flex">
              <Checkbox
                label="Atap"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.AtapTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.AtapTdkLayak"
                :value.sync="forms.AtapLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Lantai"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.LantaiTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.LantaiTdkLayak"
                :value.sync="forms.LantaiLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Dinding"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.DindingTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.DindingTdkLayak"
                :value.sync="forms.DindingLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Km. Mandi/Jamban"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.JambanTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.JambanTdkLayak"
                :value.sync="forms.KlosetLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Listrik"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.ListrikTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.ListrikTdkLayak"
                :value.sync="forms.ListrikLayak"
              />
            </div>
            <div
              style="font-weight: bold; font-size: small; padding-bottom: 10px"
            >
              Pencahayaan
            </div>
            <div style="display: flex">
              <Checkbox
                label="Jendela"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.CahayaTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.CahayaTdkLayak"
                :value.sync="forms.PeneranganLayak"
              />
            </div>
            <!-- <div style="display: flex">
              <Checkbox
                label="Pintu"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                checkedColor="error"
                :disabled="true"
                :value.sync="forms.CahayaTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.CahayaTdkLayak"
                :value.sync="forms.PintuLayak"
              />
            </div> -->
            <div
              style="font-weight: bold; font-size: small; padding-bottom: 10px"
            >
              Struktur
            </div>
            <div style="display: flex">
              <Checkbox
                label="Pondasi"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.PondasiTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.PondasiTdkLayak"
                :value.sync="forms.PondasiLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Sloof"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.SloofTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.SloofTdkLayak"
                :value.sync="forms.SloofLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Kolom"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.KolomTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.KolomTdkLayak"
                :value.sync="forms.KolomLayak"
              />
            </div>
            <div style="display: flex">
              <Checkbox
                label="Balok"
                style="width: 200px"
                checkedIcon="mdi-home-remove"
                :disabled="true"
                :value.sync="forms.BalokTdkLayak"
              />
              <Checkbox
                checkedIcon="mdi-home-heart"
                :checkedColor="'success'"
                :disabled="!forms.BalokTdkLayak"
                :value.sync="forms.BalokLayak"
              />
            </div>
          </div>
        </div>
        <div class="iblock dv-kerusakan" style="width: 100%">
          <div style="padding: 0px; font-weight: bold">TAMBAHAN SWADAYA</div>
          <div class="form-inline">
            <!-- <XSelect
              :items="[
                { key: '0', value: 'Tanpa Swadaya' },
                { key: '1', value: 'Uang' },
                { key: '2', value: 'Bahan Bangunan' },
                { key: '9', value: 'Lain-Lain' },
              ]"
              :value.sync="forms.SwadayaID"
              style="width:120px"
              width="120px"
            /> -->
            <XInput
              label="Uang"
              type="number"
              placeholder="Sebesar.."
              :value.sync="forms.SwadayaUang"
              style="width: 120px; margin-left: 8px"
            />
            <XInput
              label="Material"
              type="number"
              placeholder="Sebesar.."
              :value.sync="forms.SwadayaMaterial"
              style="width: 120px; margin-left: 8px"
            />
            <XInput
              label="Tenaga/Tukang"
              type="number"
              placeholder="Sebesar.."
              :value.sync="forms.SwadayaTenaga"
              style="width: 120px; margin-left: 8px"
            />
            <XInput
              label="Lainnya"
              type="number"
              placeholder="Sebesar.."
              :value.sync="forms.SwadayaLainnya"
              style="width: 120px; margin-left: 8px"
            />
          </div>
        </div>
      </div>
      <div
        :class="isMobile ? '' : 'iblock'"
        style="width: 460px; max-width: 100vw"
      >
        <div style="display: flex; flex-wrap: wrap">
          <Uploader
            label="RUMAH 0%"
            :disabled="true"
            :value.sync="forms.RumahDepan"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="RUMAH 50%"
            :required="true"
            :value.sync="forms.Depan50"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="RUMAH 100%"
            :required="true"
            :value.sync="forms.Depan100"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="JAMBAN 0%"
            :value.sync="forms.Jamban0"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
          <Uploader
            label="JAMBAN 100%"
            :required="!forms.Jamban100 && forms.JambanTdkLayak"
            :value.sync="forms.Jamban100"
            accept=".jpg,.jpeg,.jfif,.png,.heic"
          ></Uploader>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    forms: {},
    checkAll: false,
  }),
  props: {
    show: Boolean,
    nik: [String, Number],
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
    checkAll(val) {
      for (let x in this.forms) {
        if (x.match(/^Cek/)) this.forms[x] = val
      }
    },
  },
  computed: {},
  methods: {
    async populate() {
      let { data } = await this.$api.call('PRM.SelBansosPerson', {
        NIK: this.nik,
      })
      this.forms = data[0]
    },
    async Save() {
      let ret = await this.$api.call('PRM.SavBansosPerson', {
        ...this.forms,
        NIK: this.nik,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.modal-detail-monitoring {
  .inline-form {
    .form-coms.ui-checkbox {
      display: flex;
      .form-label {
        width: 140px;
      }
    }
  }
  .v-dialog--content {
    overflow-x: hidden;
  }
  .dv-kerusakan {
    .form-inline > .form-coms .form-label {
      width: 120px;
    }
  }
}
</style>
