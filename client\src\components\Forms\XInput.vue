<template>
  <div class="form-coms ui-input">
    <div
      class="form-label"
      :class="{ '--required': hasError }"
      v-if="$attrs.label"
    >
      <slot name="label">
        {{ $attrs.label }}
      </slot>
    </div>
    <div
      class="--pc-input"
      :style="{
        width: width,
      }"
    >
      <div
        class="--input"
        :class="{ '--type-masked': type == 'number', '--focused': isFocused }"
        :style="{
          width: width,
        }"
      >
        <v-icon left v-if="leftIcon" @click="leftIconClick">
          {{ leftIcon }}
        </v-icon>
        <div class="prefix" v-if="prefix">{{ prefix }}</div>
        <input
          :type="typeLocal"
          v-bind="$attrs"
          v-model="vmodel"
          class="--real"
          @click="handleClick($event)"
          @blur="handleBlur($event)"
          @keyup="handleKeyup($event)"
          @keydown="handleKeydown($event)"
          @change="handleChange($event)"
          :min="min"
          :style="{
            'margin-left': leftIcon ? '-36px' : undefined,
            'padding-left': leftIcon ? '30px' : undefined,
            'padding-right': rightIcon ? '30px' : undefined,
          }"
        />
        <input
          v-if="type == 'number'"
          type="text"
          v-bind="$attrs"
          v-model="mask"
          class="--mask"
          @mouseenter="handleMouseEnter"
          :style="{
            'margin-left': leftIcon ? '-36px' : undefined,
            'padding-left': leftIcon ? '30px' : undefined,
            'padding-right': rightIcon ? '30px' : undefined,
            'text-align': type == 'number' ? 'right' : 'left',
          }"
        />
        <div class="postfix" v-if="postfix">{{ postfix }}</div>
        <v-icon small right v-if="rightIconLocal" @click="rightIconClick">
          {{ rightIconLocal }}
        </v-icon>
      </div>
      <div class="--remarks" v-if="remarks">
        {{ remarks }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XInput',
  data: () => ({
    val: '',
    timeoutTracker: null,
    mask: '',
    toggleMask: true,
    isFocused: false,
    rightIconLocal: '',
    typeLocal: '',
    regex: null,
  }),
  props: {
    value: [String, Number],
    width: String,
    leftIcon: String,
    rightIcon: String,
    prefix: String,
    postfix: String,
    format: String,
    remarks: String,
    allowNegative: Boolean,
    type: {
      type: String,
      default: 'text',
    },
  },
  computed: {
    min() {
      if (this.allowNegative) return undefined
      else return 0
    },
    vmodel: {
      get() {
        return this.val === null || this.val === undefined
          ? this.value
          : this.val
      },
      set(val) {
        if (val && this.regex && !String(val).match(this.regex)) {
          return
        }
        this.val = val
        if (this.rightIcon != 'search') {
          if (this.type == 'number') {
            if (val && this.format != '#000')
              this.mask = parseFloat(val).toLocaleString()
            else this.mask = val
          }
          this.$emit('update:value', val)
        } else {
          clearTimeout(this.timeoutTracker)
          this.timeoutTracker = setTimeout(() => {
            this.$emit('update:value', val)
          }, 800)
        }
      },
    },
    hasError() {
      return (
        this.$attrs.label &&
        this.$attrs.label.match(/\*$/) &&
        (this.vmodel === null ||
          this.vmodel === undefined ||
          this.vmodel === '')
      )
    },
  },
  watch: {
    value(val) {
      this.val = val
      if (this.type == 'number') {
        if (val && this.format != '#000')
          this.mask = parseFloat(val).toLocaleString()
        else this.mask = val
      }
    },
  },
  created() {
    this.val = this.value
    this.typeLocal = this.type
    this.rightIconLocal = this.rightIcon
    if (this.type == 'number') {
      this.regex = /^[\d]+.?\d{0,}/
      if (this.val && this.format != '#000')
        this.mask = parseFloat(this.val).toLocaleString()
      else this.mask = this.val
    } else if (this.type == 'password') {
      this.rightIconLocal = 'mdi-eye-off'
    }
    if (this.$attrs.pattern) {
      this.regex = new RegExp(this.$attrs.pattern)
    }
  },
  methods: {
    handleBlur(evt) {
      this.isFocused = false
      this.$emit('blur', evt)
    },
    handleInput(evt) {
      this.$emit('input', this.vmodel, evt)
    },
    handleChange(evt) {
      this.$emit('change', this.vmodel, evt)
    },
    handleMouseEnter() {
      // this.toggleMask = false
    },
    handleClick(evt) {
      this.isFocused = true
      this.$emit('click', evt)
    },
    handleKeyup(evt) {
      this.$emit('keyup', evt)
    },
    handleKeydown() {
      // if (
      //   this.type === 'number' &&
      //   evt.key.length === 1 &&
      //   !(String(this.val || '').replace(/,/g, '') + evt.key).match(
      //     /^\d{0,}.\d{0,}$/
      //   )
      // )
      //   evt.preventDefault()
    },
    leftIconClick() {
      this.$emit('left-icon-click')
    },
    rightIconClick() {
      if (this.type == 'password') {
        if (this.rightIconLocal.match(/off$/)) {
          this.rightIconLocal = 'mdi-eye'
          this.typeLocal = 'text'
        } else {
          this.rightIconLocal = 'mdi-eye-off'
          this.typeLocal = 'password'
        }
      } else {
        this.$emit('right-icon-click')
      }
    },
  },
}
</script>
<style lang="scss">
.ui-input {
  margin-bottom: 8px;
  font-size: 14px;

  .form-label {
    text-align: left;

    &.--required {
      color: red;
    }
  }
  .--pc-input {
    width: 100%;
  }
  .--input {
    display: flex;
    // width: 180px;
    width: 100%;
    max-width: 100%;
    input {
      // padding: 4px 6px;
      // border-radius: 2px;
      padding: 6px 8px;
      border-radius: 6px;
      background: rgba(200, 200, 200, 0.2);
      border-bottom: 1px solid silver;
      width: 100%;

      &[type='number'] {
        text-align: right;
      }
      &:hover,
      &:focus {
        border: 0;
        background: rgba(200, 200, 200, 0.4);
        border-bottom: 1px solid gray;
      }
    }
    &.--type-masked {
      .--real {
        display: none;
      }
      .--mask {
        display: inline-block;
      }
      &:hover {
        .--real {
          display: inline-block;
        }
        .--mask {
          display: none;
        }
      }
      &.--focused {
        .--real {
          display: inline-block !important;
        }
        .--mask {
          display: none !important;
        }
      }
    }
    .postfix,
    .prefix {
      padding: 4px 8px;
      border-bottom: 1px solid silver;
      background: #f3f3f3;
    }
    .v-icon--right {
      margin-left: 5px;
    }
    .v-icon--right {
      margin-left: -25px;
    }
  }
  .--remarks {
    font-size: small;
    color: gray;
  }
  .v-icon.v-icon::after {
    height: 80%;
    top: 10%;
  }
}
.dense {
  .ui-input {
    font-size: 14px;
  }
}
</style>
