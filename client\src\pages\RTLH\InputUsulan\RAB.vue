<template>
  <Modal title="INPUT RAB" :show.sync="xshow" width="400px" @onSubmit="Save">
    <div class="form-inline" style="display: flex">
      <XInput
        type="number"
        label="Panjang"
        :value.sync="attrib.panjang"
        width="80px"
        postfix="m"
        @change="CalcAttrib"
      />
      <XInput
        type="number"
        label="Lebar"
        :value.sync="attrib.lebar"
        width="80px"
        postfix="m"
        @change="CalcAttrib"
        style="margin-left: -80px"
      />
    </div>
    <div style="color: red; font-size: 14px; padding-bottom: 5px">
      * Harga akan berdampak pada semua penerima di desa ini
    </div>
    <Grid
      :datagrid="datagrid"
      :disabled="true"
      :key="rebind"
      :columns="[
        { name: '', value: 'KodeRAB' },
        { name: 'Material', value: 'JenisMaterial', width: '400px' },
        { name: 'Satuan', value: 'Satuan' },
        { name: 'Volume', value: 'Volume' },
        { name: 'Harga', value: 'Harga' },
        { name: 'Total', value: 'Total' },
      ]"
    >
      <template v-slot:row-KodeRAB="{ row }">
        <div :class="'ordr-' + row.KodeRAB.length">
          {{ row.KodeRAB }}
        </div>
      </template>
      <template v-slot:row-JenisMaterial="{ row }">
        <div :class="'ordr-' + row.KodeRAB.length" style="display: flex">
          <div
            :style="
              row.JenisMaterial == 'PEKERJAAN DINDING' ||
              row.JenisMaterial == 'PEKERJAAN ATAP'
                ? 'margin-top:4px;'
                : ''
            "
          >
            {{ row.JenisMaterial }}
          </div>
          <v-spacer />
          <XSelect
            v-if="row.JenisMaterial == 'PEKERJAAN DINDING'"
            style="width: 170px; font-weight: normal"
            :items="pilihanDinding"
            :value.sync="dinding"
          />
          <XSelect
            v-if="row.JenisMaterial == 'PEKERJAAN ATAP'"
            style="width: 170px; font-weight: normal"
            :items="pilihanAtap"
            :value.sync="atap"
          />
        </div>
      </template>
      <template v-slot:row-Volume="{ row }">
        <div v-if="row.KodeRAB.length > 1" style="text-align: right">
          <!-- <XInput
            type="number"
            :value.sync="row.Volume"
            :disabled="row.KodeRAB.charAt(0) == 'E'"
            style="width:80px"
            @change="CalcHeader(row)"
          /> -->
          {{ parseInt(row.Volume) }}
        </div>
        <XSelect
          v-else-if="['A', 'B', 'C', 'D'].includes(row.KodeRAB)"
          width="70px"
          :items="discountItems"
          :value.sync="row.Volume"
          @change="CalcHeaderVol(row)"
        />
      </template>
      <template v-slot:row-Harga="{ row }">
        <div v-if="row.KodeRAB.length > 1">
          <XInput
            type="number"
            :value.sync="row.Harga"
            :disabled="row.KodeRAB.charAt(0) == 'E'"
            style="width: 100px"
            @change="CalcHeader(row)"
          />
        </div>
      </template>
      <template v-slot:row-Total="{ row }">
        <div
          v-if="row.KodeRAB.length > 1"
          :class="'ordr-' + row.KodeRAB.length"
          style="text-align: right"
        >
          Rp. {{ row.Total | format }}
        </div>
        <div
          v-else
          :class="'ordr-' + row.KodeRAB.length"
          style="text-align: right"
        >
          Rp. {{ row.Total | format }}
        </div>
        <!-- Rp. {{ parseInt(row.Total * (row.Volume || 1)) | format }} -->
      </template>
    </Grid>
    <div
      style="
        display: flex;
        font-weight: bold;
        font-size: 12px;
        padding: 8px 12px;
      "
    >
      <div>BANTUAN</div>
      <v-spacer />
      <div>Rp. 20,000,000</div>
    </div>
    <div
      style="
        display: flex;
        font-weight: bold;
        background: silver;
        font-size: 12px;
        padding: 8px 12px;
      "
    >
      <div>BANTUAN SETELAH PAJAK (14%)</div>
      <v-spacer />
      <div>Rp. 17,200,000</div>
    </div>
    <div
      style="
        display: flex;
        font-weight: bold;
        background: orange;
        font-size: 12px;
        padding: 8px 12px;
      "
    >
      <div>KISARAN SWADAYA</div>
      <v-spacer />
      <div>Rp. {{ swadaya | format }}</div>
    </div>
    <template v-slot:left-action>
      <v-btn text color="primary" @click="Download"> SIMPAN + DOWNLOAD </v-btn>
    </template>
  </Modal>
</template>
<script>
const JML_BANTUAN = 17200000
export default {
  data: () => ({
    xshow: false,
    datagrid: [],
    rebind: 1,
    swadaya: 0,
    discountItems: [
      { val: '0.25', txt: '25%' },
      { val: '0.33', txt: '33%' },
      { val: '0.50', txt: '50%' },
      { val: '0.66', txt: '66%' },
      { val: '0.75', txt: '75%' },
      { val: '1', txt: '100%' },
    ],
    pilihanAtap: [
      { val: 'atap_baja', txt: 'Baja Ringan' },
      { val: 'atap_kayu', txt: 'Kayu' },
    ],
    pilihanDinding: [
      { val: 'bata_ringan', txt: 'Bata Ringan' },
      { val: 'bata_merah', txt: 'Bata Merah' },
    ],
    atap: 'atap_baja',
    dinding: 'bata_ringan',
    attrib: {
      panjang: 0,
      lebar: 0,
      atap: {
        rangka: 0,
        reng: 0,
        penutup: 0,
        nok: 0,
      },
      dinding: {
        depan: 0,
        kanan: 0,
        kiri: 0,
        belakang: 0,

        total: 0,
        plester: 0,
      },
      lantai: 0,
      gunungan: 0,
      bata: 0,
      semen: 0,
      kusen: {
        pintu: 3.864,
        jendela: 3.84,
      },
    },
  }),
  props: {
    show: Boolean,
    NIK: [Object, String, Number],
    tahun: [String, Number],
    filters: Array,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (val) this.Populate()
      else this.Reset()
      this.$emit('update:show', val)
    },
    atap() {
      this.Populate('B')
    },
  },
  mounted() {
    this.Populate()
  },
  methods: {
    Reset() {
      this.atap = 'atap_baja'
      this.dinding = 'bata_ringan'
    },
    async Populate(forceJenis) {
      let res = await this.$api.call('PRM.SelRABDet', {
        NIK: this.NIK,
        Tahun: this.tahun,
        Jenis: forceJenis ? this.atap : null,
      })
      if (res.data.length) {
        this.attrib.panjang = res.data[0].Panjang
        this.attrib.lebar = res.data[0].Lebar

        if (!forceJenis) {
          let f = res.data.find((d) =>
            ['atap_baja', 'atap_kayu'].includes(d.Jenis)
          )
          this.atap = f.Jenis
        }
      }
      let datagrid = res.data.filter((d) =>
        this.filters.includes(d.KodeRAB.charAt(0))
      )
      if (forceJenis) {
        datagrid = datagrid.filter((d) => d.KodeRAB.charAt(0) == forceJenis)
        this.datagrid = this.datagrid
          .filter((d) => d.KodeRAB.charAt(0) != forceJenis && d.KodeRAB != '-')
          .concat(datagrid)
          .sort((a, b) => {
            if (a.KodeRAB < b.KodeRAB) return -1
            else if (a.KodeRAB > b.KodeRAB) return 1
            else return 0
          })
      } else {
        this.datagrid = datagrid
      }
      this.datagrid.push({
        KodeRAB: '-',
        JenisMaterial: 'TOTAL',
        Volume: null,
        Harga: null,
        Total: 0,
      })
      this.datagrid.push({
        KodeRAB: '-',
        JenisMaterial: 'DIBULATKAN',
        Volume: null,
        Harga: null,
        Total: 0,
      })
      this.CalcAttrib(forceJenis)
    },
    CalcHeaderVol(row) {
      let code = row.KodeRAB.charAt(0)
      let total = 0
      for (let r of this.datagrid) {
        if (r.KodeRAB.length > 1 && r.KodeRAB.charAt(0) == code) {
          total += r.Total
        }
      }
      let head = this.datagrid.find((r) => r.KodeRAB == code)
      head.Total = parseInt(total * (head.Volume || 1))
      // console.log(head)
      this.CalcGrandTotal()
      // this.rebind++
    },
    CalcHeader(row) {
      row.Total = row.Volume * row.Harga
      // let code = row.KodeRAB.charAt(0)
      // let total = 0
      // for (let r of this.datagrid) {
      //   if (r.KodeRAB.length > 1 && r.KodeRAB.charAt(0) == code) {
      //     total += r.Total
      //   }
      // }
      // let head = this.datagrid.find((r) => r.KodeRAB == code)
      // head.Total = parseInt(total * (head.Volume || 1))
      // this.CalcGrandTotal()
      this.CalcHeaderVol(row)
      // this.rebind++
    },
    CalcGrandTotal() {
      let total = 0
      for (let r of this.datagrid) {
        if (this.filters.includes(r.KodeRAB)) {
          total += r.Total
        }
      }
      this.datagrid[this.datagrid.length - 2].Total = parseInt(total)

      let dibulatkan = Math.ceil(total / 100000) * 100000
      this.datagrid[this.datagrid.length - 1].Total = dibulatkan
      this.swadaya = dibulatkan > JML_BANTUAN ? dibulatkan - JML_BANTUAN : 0
      // this.rebind++
    },
    CalcAttrib(forceJenis) {
      let panjang =
        this.attrib.panjang >= this.attrib.lebar
          ? this.attrib.panjang
          : this.attrib.lebar

      let lebar =
        this.attrib.panjang >= this.attrib.lebar
          ? this.attrib.lebar
          : this.attrib.panjang

      // dinding
      this.attrib.dinding.depan = lebar * 3
      this.attrib.dinding.kanan = panjang * 3
      this.attrib.dinding.kiri = panjang * 3
      this.attrib.dinding.belakang = lebar * 3
      this.attrib.gunungan = ((lebar * 2.12) / 2) * 2

      let dindingTotal =
        this.attrib.dinding.depan +
        this.attrib.dinding.kanan +
        this.attrib.dinding.kiri +
        this.attrib.dinding.belakang +
        this.attrib.gunungan -
        this.attrib.kusen.pintu -
        this.attrib.kusen.jendela
      this.attrib.dinding.total = dindingTotal * 1.05

      // atap
      this.attrib.atap.rangka = ((panjang * lebar) / 1.565) * 1.05
      this.attrib.atap.reng = ((panjang * lebar) / 1.2) * 1.05
      this.attrib.atap.nok = Math.ceil(panjang / 0.8)

      // lantai
      this.attrib.lantai = panjang * lebar

      this.attrib.bata = Math.ceil(this.attrib.dinding.total * 8.4)
      this.attrib.semen = Math.ceil(this.attrib.dinding.total * 0.1)
      this.attrib.dinding.plester = (
        dindingTotal * 2 -
        this.attrib.gunungan
      ).toFixed(2)
      this.UpdateVolume(
        ['A', 'B', 'C', 'D', 'E'].includes(forceJenis) ? forceJenis : null
      )
    },
    UpdateVolume(forceJenis) {
      if (forceJenis == 'A' || !forceJenis) {
        this.SetVolume('A.1', this.attrib.bata)
        this.SetVolume('A.2', this.attrib.semen)
        this.SetVolume('A.3', this.attrib.dinding.plester)
        this.SetVolume('A.4', this.attrib.dinding.plester)
        this.SetVolume('A.5', this.attrib.dinding.plester)
      }
      if (forceJenis == 'B' || !forceJenis) {
        this.SetVolume('B.1', this.attrib.atap.rangka)
        this.SetVolume('B.2', this.attrib.atap.reng)
        this.SetVolume('B.4', this.attrib.atap.nok)
      }
      if (forceJenis == 'C' || !forceJenis) {
        this.SetVolume('C.1', this.attrib.lantai)
      }
      for (let x of this.filters) this.CalcHeader({ KodeRAB: x })
      this.CalcGrandTotal()
    },
    SetVolume(kode, vol) {
      let cell = this.datagrid.find((r) => r.KodeRAB == kode)
      if (cell) {
        cell.Volume = vol
        this.CalcTotalRow(cell)
      }
    },
    CalcTotalRow(row) {
      row.Total = Math.ceil(row.Volume * row.Harga)
    },
    async Download() {
      // window.open(
      //   this.$api.url +
      //     '/reports/template/RAB.ods?spt=PRM_SelRABDet&NIK=' +
      //     this.NIK
      // )
      let ret = await this.$api.post('/reports/template/RAB.ods', {
        sp: 'PRM_RptRAB',
        NIK: this.NIK,
      })
      if (ret.success) this.$api.download('/report/' + ret.data)
    },
    async Save() {
      let ret = await this.$api.call('PRM.SavRAB', {
        NIK: this.NIK,
        Tahun: this.tahun,
        Panjang: this.attrib.panjang,
        Lebar: this.attrib.lebar,
        Total: this.swadaya + JML_BANTUAN,
        Swadaya: this.swadaya,
        XmlRAB: this.datagrid,
      })
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
<style lang="scss">
.ordr-1 {
  font-weight: bold;
}
</style>
