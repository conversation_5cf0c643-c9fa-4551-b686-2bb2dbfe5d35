<template>
  <Page title="Validasi Data Backlog" :sidebar="true">
    <Sidebar :value.sync="area" />
    <div style="width: calc(100vw - 330px)" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        @clickTambahBaru="ClickTambahBaru"
        :addNew="user?.RolePositionID != 15"
      />
      <Grid
        :datagrid.sync="datagrid"
        dbref="BLG.ValidasiData"
        :dbparams="area"
        :disabled="true"
        height="calc(100vh - 216px)"
        :columns="[
          {
            name: 'NIK',
            value: 'NIK',
            filter: {
              type: 'search',
            },
          },
          {
            name: '<PERSON>a',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width plain',
            filter: {
              type: 'search',
            },
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: '<PERSON><PERSON><PERSON>',
            value: '<PERSON>ama<PERSON>',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'IDBDT',
            value: 'IDBDT',
            class: 'center',
          },
          {
            name: '<PERSON><PERSON>',
            value: 'StatusTanah',
          },
          {
            name: '<PERSON><PERSON>',
            value: 'LuasTanah',
          },
          {
            name: 'Skor',
            value: 'SkorTag',
          },
          {
            name: 'DT',
            value: 'NamaData',
            width: '70px',
            filter: {
              type: 'select',
              value: 'TipeData',
              text: 'NamaData',
              dbref: 'PRM.SelTipeData',
              dbparams: {},
            },
          },
          {
            name: 'Intervensi',
            value: 'Intervensi',
            filter: {
              type: 'select',
              value: 'SumberID',
              text: 'SumberName',
              dbref: 'PRM.SelSumber',
              dbparams: { BelumPernah: 1 },
            },
          },
          {
            name: 'Updated',
            value: 'ModifiedDate',
          },
        ]"
      >
        <template v-slot:row-NIK="{ row }">
          <nik-block :nik="row.NIK" />
        </template>
        <template v-slot:row-KRT_Nama="{ row }">
          <v-btn text small color="primary" @click.stop="OpenDetail(row.NoRef)">
            {{ row.KRT_Nama }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
            >mdi-account-check</v-icon
          >
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-IDBDT="{ row }">
          <v-icon
            v-tooltip="row.IDBDT"
            v-show="row.IDBDT"
            @click="CopyText(row.IDBDT)"
          >
            mdi-content-copy
          </v-icon>
        </template>
        <template v-slot:row-NamaData="{ row }">
          <span>{{ row.NamaData }}</span>
          <span
            v-if="row.DataTag"
            style="
              font-size: 10px;
              margin-left: 5px;
              background: lightblue;
              padding: 3px;
              border-radius: 3px;
            "
          >
            {{ row.DataTag }}
          </span>
        </template>
        <template v-slot:row-ModifiedDate="{ row }">
          <span
            v-show="row.ModifiedDate"
            :class="
              row.UpdatedMonth < 10
                ? 'bold'
                : row.UpdatedMonth > 30
                ? 'gray'
                : ''
            "
            >{{ row.ModifiedDate | format }}</span
          >
        </template>
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from './SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from './ValidasiDetail.vue'
import { mapGetters } from 'vuex'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
  },
  data: () => ({
    datagrid: [],
    area: {},
    showDetailModal: false,
    selectedRef: null,
  }),
  computed: {
    ...mapGetters({
      user: 'getUser',
    }),
  },
  methods: {
    CopyText(txt) {
      navigator.clipboard.writeText(txt)
      this.$api.notify('IDBDT sudah tersalin')
    },
    ClickTambahBaru() {
      this.selectedRef = null
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
  },
}
</script>
